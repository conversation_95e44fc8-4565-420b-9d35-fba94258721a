# <PERSON><PERSON> and Token Helpers Documentation

This document explains the separation of concerns between `cacheHelper` and `tokenHelper` utilities, along with the new constants and type organization.

## Overview

The authentication system uses four separate files for better code organization:

- **`cacheHelper.ts`** - General localStorage operations with encryption/decryption
- **`tokenHelper.ts`** - Token-specific operations using cacheHelper methods
- **`cache.constants.ts`** - Centralized cache key constants
- **`navigation.types.ts`** - Navigation and location type definitions

## Cache Helper (`src/utils/cacheHelper.ts`)

Provides general-purpose localStorage operations with optional encryption.

### Secure (Encrypted) Operations

```typescript
import {setSecureItem, getSecureItem, setSecureObject, getSecureObject} from '@/utils/cacheHelper';

// Store encrypted string
setSecureItem('userPreference', 'darkMode');

// Retrieve encrypted string
const preference = getSecureItem('userPreference'); // 'darkMode'

// Store encrypted object
setSecureObject('userSettings', {theme: 'dark', language: 'en'});

// Retrieve encrypted object
const settings = getSecureObject<{theme: string; language: string}>('userSettings');
```

### Plain (Unencrypted) Operations

```typescript
import {setItem, getItem, setObject, getObject} from '@/utils/cacheHelper';

// Store plain string
setItem('tempData', 'someValue');

// Retrieve plain string
const data = getItem('tempData');

// Store plain object
setObject('config', {apiUrl: 'https://api.example.com'});

// Retrieve plain object
const config = getObject<{apiUrl: string}>('config');
```

### Utility Operations

```typescript
import {removeItem, removeItems, clearAll, hasItem, getAllKeys} from '@/utils/cacheHelper';

// Remove single item
removeItem('tempData');

// Remove multiple items
removeItems(['item1', 'item2', 'item3']);

// Clear all localStorage
clearAll();

// Check if item exists
const exists = hasItem('userPreference'); // boolean

// Get all keys
const keys = getAllKeys(); // string[]
```

## Token Helper (`src/utils/tokenHelper.ts`)

Provides token-specific operations using cacheHelper for storage.

### Token Storage Operations

```typescript
import {
  storeAccessToken,
  getAccessToken,
  storeRefreshToken,
  getRefreshToken,
  storeTokenExpiration,
  getTokenExpiration,
  isTokenExpired,
  clearTokens,
} from '@/utils/tokenHelper';

// Store tokens (automatically encrypted)
storeAccessToken('your-access-token');
storeRefreshToken('your-refresh-token');
storeTokenExpiration(3600); // 1 hour in seconds

// Retrieve tokens (automatically decrypted)
const accessToken = getAccessToken();
const refreshToken = getRefreshToken();
const expiration = getTokenExpiration();

// Check expiration
const expired = isTokenExpired(); // boolean

// Clear all tokens
clearTokens();
```

### URL Token Operations

```typescript
import {extractTokenFromUrl, removeTokenFromUrl} from '@/utils/tokenHelper';

// Extract token from URL
const token = extractTokenFromUrl(location); // string | null

// Remove token from URL
removeTokenFromUrl(location, navigate);
```

## Key Benefits of Separation

### 1. **Single Responsibility**

- `cacheHelper`: Handles all localStorage operations with encryption
- `tokenHelper`: Handles token-specific business logic

### 2. **Reusability**

- `cacheHelper` can be used for any data storage needs
- `tokenHelper` is focused only on authentication tokens

### 3. **Maintainability**

- Encryption logic is centralized in `cacheHelper`
- Token operations are clearly defined in `tokenHelper`

### 4. **Type Safety**

- Both helpers provide full TypeScript support
- Generic types for object storage operations

## Environment Variables

```env
# For cacheHelper encryption
VITE_CACHE_SECRET_KEY=your-cache-encryption-key

# Legacy token key (now uses VITE_CACHE_SECRET_KEY)
VITE_TOKEN_SECRET_KEY=your-token-encryption-key
```

## Usage Examples

### Storing User Preferences (Encrypted)

```typescript
import {setSecureObject, getSecureObject} from '@/utils/cacheHelper';

interface UserPreferences {
  theme: 'light' | 'dark';
  language: string;
  notifications: boolean;
}

// Store
const preferences: UserPreferences = {
  theme: 'dark',
  language: 'en',
  notifications: true,
};
setSecureObject('userPreferences', preferences);

// Retrieve
const savedPreferences = getSecureObject<UserPreferences>('userPreferences');
```

### Storing Configuration (Plain)

```typescript
import {setObject, getObject} from '@/utils/cacheHelper';

interface AppConfig {
  apiUrl: string;
  version: string;
}

// Store
const config: AppConfig = {
  apiUrl: 'https://api.example.com',
  version: '1.0.0',
};
setObject('appConfig', config);

// Retrieve
const savedConfig = getObject<AppConfig>('appConfig');
```

### Complete Authentication Flow

```typescript
import {useAuth} from '@/hooks';
import {storeAccessToken, storeRefreshToken, storeTokenExpiration} from '@/utils/tokenHelper';

const handleLogin = async credentials => {
  const response = await loginAPI(credentials);

  // Store tokens using tokenHelper
  storeAccessToken(response.access_token);
  storeRefreshToken(response.refresh_token);
  storeTokenExpiration(response.expires_in);

  // Update Redux state
  login(response);
};
```

## Error Handling

Both helpers include comprehensive error handling:

- **Encryption/Decryption errors**: Logged and return null
- **JSON parsing errors**: Logged and return null
- **localStorage errors**: Logged and fail gracefully
- **Invalid parameters**: Validated and ignored

## Security Notes

1. **Encryption**: Uses AES encryption for sensitive data
2. **Key Management**: Encryption keys should be stored in environment variables
3. **Error Logging**: Errors are logged but don't expose sensitive information
4. **Validation**: All inputs are validated before processing

This separation provides a clean, maintainable, and secure approach to data storage in the application.
