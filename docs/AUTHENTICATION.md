# Authentication System Documentation

This document describes the comprehensive token-based authentication system implemented in the application.

## Overview

The authentication system provides:
- Secure token storage with encryption
- Automatic token refresh
- Permission-based access control
- Protected routes
- Token expiration monitoring
- Comprehensive error handling

## Core Components

### 1. Token Helper (`src/utils/tokenHelper.ts`)

Provides secure token storage and management:

```typescript
import {
  storeAccessToken,
  getAccessToken,
  storeRefreshToken,
  getRefreshToken,
  isTokenExpired,
  clearTokens
} from '@/utils/tokenHelper';

// Store tokens securely (encrypted)
storeAccessToken('your-access-token');
storeRefreshToken('your-refresh-token');

// Retrieve tokens
const accessToken = getAccessToken();
const refreshToken = getRefreshToken();

// Check expiration
const expired = isTokenExpired();

// Clear all tokens
clearTokens();
```

### 2. Auth Redux Slice (`src/redux/auth/authSlice.ts`)

Manages authentication state:

```typescript
import {useAppDispatch} from '@/hooks';
import {setCredentials, unsetCredentials} from '@/redux/auth/authSlice';

const dispatch = useAppDispatch();

// Login
dispatch(setCredentials({
  access_token: 'token',
  refresh_token: 'refresh',
  expires_in: 3600,
  // ... other auth data
}));

// Logout
dispatch(unsetCredentials());
```

### 3. useAuth Hook (`src/hooks/useAuth/useAuth.ts`)

Primary authentication hook:

```typescript
import {useAuth} from '@/hooks';

const {
  isAuthenticated,
  accessToken,
  refreshToken,
  permissions,
  login,
  logout,
  setUserPermissions,
  checkTokenExpiration,
  hasPermission
} = useAuth();

// Login user
login(authData);

// Logout user
logout();

// Check permissions
const canAccess = hasPermission(['READ', 'WRITE']);
```

### 4. API Interceptor (`src/redux/apiSlice.ts`)

Handles automatic token refresh:

- Automatically adds Authorization header
- Refreshes expired tokens
- Retries failed requests after refresh
- Logs out user if refresh fails

### 5. Protected Routes (`src/routes/components/ProtectedRoute/ProtectedRoute.tsx`)

Protects routes based on authentication and permissions:

```typescript
import {ProtectedRoute} from '@/routes/components/ProtectedRoute';

// Require authentication only
<ProtectedRoute>
  <YourComponent />
</ProtectedRoute>

// Require specific permissions
<ProtectedRoute requiredPermissions={['ADMIN', 'WRITE']}>
  <AdminComponent />
</ProtectedRoute>

// Custom redirect
<ProtectedRoute redirectTo="/custom-login">
  <YourComponent />
</ProtectedRoute>
```

### 6. Token Monitor (`src/hooks/useTokenMonitor/useTokenMonitor.ts`)

Monitors token expiration and handles automatic refresh:

```typescript
import {useTokenMonitor} from '@/hooks';

const {isRefreshing, checkTokenExpiration, attemptRefresh} = useTokenMonitor({
  checkInterval: 60000, // Check every minute
  refreshBeforeExpiry: 5, // Refresh 5 minutes before expiry
  autoRefresh: true,
  onTokenExpiring: () => console.log('Token expiring...'),
  onTokenExpired: () => console.log('Token expired'),
  onRefreshSuccess: () => console.log('Refresh successful'),
  onRefreshError: (error) => console.error('Refresh failed:', error),
});
```

### 7. Auth Provider (`src/providers/AuthProvider/AuthProvider.tsx`)

Global authentication provider with monitoring:

```typescript
import {AuthProvider} from '@/providers/AuthProvider';

function App() {
  return (
    <AuthProvider>
      <YourApp />
    </AuthProvider>
  );
}
```

### 8. Authentication API (`src/redux/auth/authApiSlice.ts`)

API endpoints for authentication:

```typescript
import {
  useLoginMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useGetUserProfileQuery,
  useValidateTokenQuery
} from '@/redux/auth/authApiSlice';

// Login
const [login, {isLoading}] = useLoginMutation();
const result = await login({username, password}).unwrap();

// Logout
const [logout] = useLogoutMutation();
await logout().unwrap();

// Get user profile
const {data: profile} = useGetUserProfileQuery();
```

## Usage Examples

### Basic Setup

1. Wrap your app with AuthProvider:

```typescript
import {AuthProvider} from '@/providers/AuthProvider';

function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/*" element={
            <ProtectedRoute>
              <MainLayout />
            </ProtectedRoute>
          } />
        </Routes>
      </Router>
    </AuthProvider>
  );
}
```

2. Create a login component:

```typescript
import {useAuth} from '@/hooks';
import {useLoginMutation} from '@/redux/auth/authApiSlice';

const Login = () => {
  const {login} = useAuth();
  const [loginMutation] = useLoginMutation();

  const handleLogin = async (credentials) => {
    const result = await loginMutation(credentials).unwrap();
    login(result);
  };

  // ... rest of component
};
```

### Permission-Based Access

```typescript
import {useAuth} from '@/hooks';

const AdminPanel = () => {
  const {hasPermission} = useAuth();

  if (!hasPermission(['ADMIN'])) {
    return <div>Access denied</div>;
  }

  return <div>Admin content</div>;
};
```

### Manual Token Refresh

```typescript
import {useTokenMonitor} from '@/hooks';

const SomeComponent = () => {
  const {attemptRefresh} = useTokenMonitor();

  const handleRefresh = async () => {
    const success = await attemptRefresh();
    if (success) {
      console.log('Token refreshed successfully');
    }
  };

  return <button onClick={handleRefresh}>Refresh Token</button>;
};
```

## Security Features

1. **Token Encryption**: All tokens are encrypted before storage using AES encryption
2. **Automatic Expiration**: Tokens are automatically checked for expiration
3. **Secure Storage**: Uses localStorage with encryption (consider httpOnly cookies for production)
4. **Permission Validation**: Route and component-level permission checking
5. **Automatic Cleanup**: Tokens are cleared on logout or expiration

## Environment Variables

```env
VITE_TOKEN_SECRET_KEY=your-secret-key-for-encryption
VITE_AGENT_PORTAL_FACADE_API_BASE_URL=your-api-base-url
```

## Best Practices

1. Always use the `useAuth` hook for authentication operations
2. Wrap protected routes with `ProtectedRoute` component
3. Use the `AuthProvider` at the app root level
4. Handle authentication errors gracefully with toast notifications
5. Validate permissions at both route and component levels
6. Monitor token expiration with `useTokenMonitor`
7. Use environment variables for sensitive configuration

## Error Handling

The system handles various error scenarios:
- Token expiration
- Refresh token failure
- Network errors
- Invalid credentials
- Permission denied

All errors are logged and user-friendly messages are displayed via toast notifications.
