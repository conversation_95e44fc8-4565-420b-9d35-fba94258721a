'use client';

import type {ColumnDef} from '@tanstack/react-table';
import AvailabilityStatus from '@/components/ui/availabilityStatus';
import NameWithAvatar from '@/components/name-avtar';
import StatusSwitch from '@/components/status-switch';
import {AgentStatus, type AgentsDetails} from '@/types';
import {Button} from '@/components/ui/button';
import {Pencil, Trash2} from 'lucide-react';
import {formatDate} from '@/utils';
import type {TFunction} from 'i18next';

export const agentColumns = (t: TFunction<'translation', undefined>): ColumnDef<AgentsDetails>[] => [
  {
    accessorKey: 'name',
    header: t('NAME'),
    id: 'name',
    cell: ({row}) => (
      <div className="text-left">
        <NameWithAvatar name={row.getValue('name')} />
      </div>
    ),
  },
  {
    accessorKey: 'email',
    id: 'email',
    header: t('EMAIL'),
    cell: ({row}) => <div className="text-left">{row.getValue('email')}</div>,
  },
  {
    accessorKey: 'mobile',
    id: 'mobile',
    header: t('MOBILE'),
    cell: ({row}) => <div className="text-left">{row.getValue('mobile')}</div>,
  },
  {
    accessorKey: 'departmentName',
    id: 'department',
    header: t('DEPARTMENT'),
    cell: ({row}) => <div className="text-left">{row.getValue('department')}</div>,
  },
  {
    accessorKey: 'createdAt',
    id: 'createdAt',
    header: t('CREATED_ON'),
    cell: ({row}) => <div className="text-left">{formatDate(row.getValue('createdAt'))}</div>,
  },
  {
    accessorKey: 'availability',
    header: t('AVAILABILITY'),
    cell: ({row}) => (
      <div className="flex justify-start">
        <AvailabilityStatus status={row.getValue('availability')} />
      </div>
    ),
  },
  {
    accessorKey: 'status',
    header: t('STATUS'),
    cell: ({row}) => {
      const isActive = row.getValue('status') === AgentStatus.ACTIVE;
      return (
        <div className="flex justify-center">
          <StatusSwitch
            isActive={isActive}
            onStatusChange={checked => {
              console.log(checked);
              // Handle the status change
            }}
          />
        </div>
      );
    },
  },
  {
    id: 'actions',
    header: t('ACTIONS'),
    cell: () => (
      <div className="flex items-center justify-center gap-0 h-full">
        <Button variant="ghost" size="icon" aria-label={t('EDIT')}>
          <Pencil className="h-2 w-2 text-gray-500" />
        </Button>
        <Button variant="ghost" size="icon" aria-label={t('DELETE')}>
          <Trash2 className="h-2 w-2 text-gray-500" />
        </Button>
      </div>
    ),
  },
];
