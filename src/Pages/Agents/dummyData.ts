import type {AgentsDetails} from '@/types';
import {AgentAvailability} from '@/types';

export const dummyAgents: AgentsDetails[] = [
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000001',
    departmentName: 'Customer Experience',
    createdAt: '2024-06-01T08:00:00Z',
    availability: AgentAvailability.ONLINE,
    status: 'active',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000002',
    departmentName: 'Customer Experience',
    createdAt: '2024-06-02T08:00:00Z',
    availability: AgentAvailability.BUSY,
    status: 'active',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000003',
    departmentName: 'Customer Experience',
    createdAt: '2024-06-10T08:00:00Z',
    availability: AgentAvailability.AWAY,
    status: 'inactive',
  },
  {
    name: '<PERSON>',
    email: '<EMAIL>',
    mobile: '+919999000004',
    departmentName: 'Customer Experience',
    createdAt: '2024-06-10T08:00:00Z',
    availability: AgentAvailability.OFFLINE,
    status: 'inactive',
  },
  {
    name: 'Emma Mark',
    email: '<EMAIL>',
    mobile: '+919999000005',
    departmentName: 'Customer Experience',
    createdAt: '2024-06-12T08:00:00Z',
    availability: AgentAvailability.ONLINE,
    status: 'active',
  },
  {
    name: 'Liam Bose',
    email: '<EMAIL>',
    mobile: '+919999000006',
    departmentName: 'Customer Experience',
    createdAt: '2024-06-12T08:00:00Z',
    availability: AgentAvailability.OFFLINE,
    status: 'inactive',
  },
  {
    name: 'Olivia Martinez',
    email: '<EMAIL>',
    mobile: '+919999000007',
    departmentName: 'Technical Assistance',
    createdAt: '2025-06-07T08:00:00Z',
    availability: AgentAvailability.ONLINE,
    status: 'active',
  },
  {
    name: 'Noah Thompson',
    email: '<EMAIL>',
    mobile: '+919999000008',
    departmentName: 'Technical Assistance',
    createdAt: '2025-06-08T08:00:00Z',
    availability: AgentAvailability.OFFLINE,
    status: 'inactive',
  },
];
