'use client';

import {CommonBreadcrumb} from '@/components/BreadCrumbs/CommonBreadCrumbs';
import type {AgentFormValues} from '@/validations/agentSchema';
import SuccessModal from '@/components/modals/SuccessModal';
import {useCreateAgentMutation} from '../../redux/agents/agentSlice';
import {useState} from 'react';
import {useNavigate} from 'react-router-dom';
import {AgentForm} from '@/Pages/Agents/AgentForm';
import {MESSAGES} from '@/constants';
import {useTranslation} from 'react-i18next';

const AddAgentPage = () => {
  const {t} = useTranslation();
  const [successModalOpen, setSuccessModalOpen] = useState<boolean>(false);
  const [createAgent, {isLoading: isCreating}] = useCreateAgentMutation();
  const navigate = useNavigate();
  const handleSubmitAgent = async (values: AgentFormValues) => {
    try {
      await createAgent({
        name: values.name,
        email: values.email,
        mobile: values.mobile,
        departmentName: values.department,
      }).unwrap();
      setSuccessModalOpen(true);
    } catch (error) {
      console.error(MESSAGES.AGENT_CREATION_FAILED, error);
    }
  };

  return (
    <div className="ml-6 w-full pr-6">
      <CommonBreadcrumb items={[{label: t('TEAM'), href: '/teams'}, {label: t('ADD_AGENT_CRUMB')}]} />
      <AgentForm onSubmit={values => void handleSubmitAgent(values)} isLoading={isCreating} />
      <SuccessModal
        isOpen={successModalOpen}
        onClose={() => {
          setSuccessModalOpen(false);
          void navigate('/teams'); // Redirect to the teams page after success
        }}
        message={t(MESSAGES.AGENT_CREATED)}
      />
    </div>
  );
};
export default AddAgentPage;
