'use client';

import {agentColumns} from './AgentColumns';
import {DataTable} from '../../components/data-table';
import {useGetAgentsQuery} from '../../redux/agents/agentSlice';
import {useNavigate} from 'react-router-dom';
import {AgentAvailability, FilterItemType, type AppliedFilter, type FilterGroup} from '@/types';
import {useState} from 'react';
import {dummyAgents} from './dummyData';
import {useTranslation} from 'react-i18next';

const AgentsPage = () => {
  const {t} = useTranslation();
  const navigate = useNavigate();
  const [appliedFilters, setAppliedFilters] = useState<AppliedFilter | null>(null);

  const filterGroups: FilterGroup[] = [
    {
      id: 'departments',
      name: 'Departments',
      items: [
        {id: 'hr', label: 'Human Resources', type: FilterItemType.CHECKBOX},
        {id: 'it', label: 'IT', type: FilterItemType.CHECKBOX},
        {id: 'finance', label: 'Finance', type: FilterItemType.CHECKBOX},
      ],
    },
    {
      id: 'availability',
      name: 'Availability',
      items: [
        {id: AgentAvailability.ONLINE, label: t('ONLINE'), type: FilterItemType.CHIP},
        {id: AgentAvailability.AWAY, label: t('AWAY'), type: FilterItemType.CHIP},
        {id: AgentAvailability.BUSY, label: t('BUSY'), type: FilterItemType.CHIP},
        {id: AgentAvailability.OFFLINE, label: t('OFFLINE'), type: FilterItemType.CHIP},
      ],
    },
    {
      id: 'status',
      name: 'Status',
      items: [
        {id: 'active', label: t('ACTIVE'), type: FilterItemType.CHIP},
        {id: 'inactive', label: t('INACTIVE'), type: FilterItemType.CHIP},
      ],
    },
  ];

  const {data: agents} = useGetAgentsQuery();

  const handleAddAgent = () => {
    // eslint-disable-next-line @typescript-eslint/no-floating-promises
    navigate('/addAgent');
  };

  const handleSearch = (value: string) => {
    // Implement search functionality here
    // This could involve updating a state variable that filters the agents data
    console.log('Search value:', value);
  };

  const handleApplyFilters = (activeFilters: AppliedFilter | null) => {
    console.log('Active filters:', activeFilters);
    setAppliedFilters(activeFilters);
  };

  return (
    <div className="w-full">
      <DataTable
        columns={agentColumns(t)}
        data={agents?.data ?? dummyAgents}
        buttonLabel={t('ADD_AGENT')}
        searchPlaceholder={t('SEARCH')}
        onSearch={handleSearch}
        onButtonClick={handleAddAgent}
        filterGroups={filterGroups}
        handleApplyFilters={handleApplyFilters}
        activeFilters={appliedFilters}
      />
    </div>
  );
};

export default AgentsPage;
