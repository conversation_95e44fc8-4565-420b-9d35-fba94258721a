import {describe, it, expect, vi, beforeEach} from 'vitest';
import {render, screen, waitFor} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import {AgentForm} from '@/Pages/Agents/AgentForm';
import * as reactRouter from 'react-router-dom';
import * as departmentSlice from '@/redux/departments/departmentSlice';

// Mock dependencies
vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('@/redux/departments/departmentSlice', () => ({
  useGetDepartmentsQuery: vi.fn(),
}));

describe('AgentForm', () => {
  const mockOnSubmit = vi.fn();
  const mockNavigate = vi.fn();
  const mockDepartments = {
    data: [
      {id: '1', name: 'HR'},
      {id: '2', name: 'IT'},
    ],
  };

  beforeEach(() => {
    vi.clearAllMocks();
    (reactRouter.useNavigate as any).mockReturnValue(mockNavigate);
    (departmentSlice.useGetDepartmentsQuery as any).mockReturnValue({
      data: mockDepartments,
      isLoading: false,
    });
    Element.prototype.hasPointerCapture = () => false;
    Element.prototype.scrollIntoView = () => {};
  });

  it('renders the form with all fields', () => {
    render(<AgentForm onSubmit={mockOnSubmit} />);

    expect(screen.getByTestId('Name')).toBeInTheDocument();
    expect(screen.getByTestId('Email')).toBeInTheDocument();
    expect(screen.getByText('MOBILE')).toBeInTheDocument();
    expect(screen.getByText('DEPARTMENT')).toBeInTheDocument();
    expect(screen.getByText('CANCEL_ADDING')).toBeInTheDocument();
    expect(screen.getByText('ADD')).toBeInTheDocument();
  });

  it('navigates back when Cancel button is clicked', async () => {
    const user = userEvent.setup();
    render(<AgentForm onSubmit={mockOnSubmit} />);

    await user.click(screen.getByText('CANCEL_ADDING'));
    expect(mockNavigate).toHaveBeenCalledWith('/teams');
  });

  it('submits the form with valid data', async () => {
    const user = userEvent.setup();
    render(<AgentForm onSubmit={mockOnSubmit} />);

    // Fill in the form fields
    await user.type(screen.getByTestId('Name'), 'John Doe');
    await user.type(screen.getByTestId('Email'), '<EMAIL>');
    const phoneInput = screen.getByPlaceholderText('');
    await user.type(phoneInput, '9876543210');

    // Select department
    await user.click(screen.getByTestId('department-trigger'));
    await user.click(screen.getByTestId('department-option-HR'));

    // Submit the form
    await user.click(screen.getByText('ADD'));

    // Check that onSubmit was called with the correct data
    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'John Doe',
          email: '<EMAIL>',
          mobile: '+919876543210',
          department: '1',
        }),
      );
    });
  });

  it('disables the submit button when form is invalid', () => {
    render(<AgentForm onSubmit={mockOnSubmit} />);

    // Button should be disabled initially
    expect(screen.getByText('ADD')).toBeDisabled();
  });

  it('shows loading state when isLoading is true', () => {
    render(<AgentForm onSubmit={mockOnSubmit} isLoading={true} />);

    expect(screen.getByText('ADDING...')).toBeInTheDocument();
    expect(screen.getByText('ADDING...')).toBeDisabled();
  });
});
