import {describe, it, expect, vi, beforeEach, afterEach} from 'vitest';
import {render, screen} from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import AgentsPage from '../AgentsPage';
import * as reactRouter from 'react-router-dom';
import * as reactRedux from '@/redux/agents/agentSlice';

vi.mock('react-router-dom', () => ({
  useNavigate: vi.fn(),
}));

vi.mock('@/redux/agents/agentSlice', () => ({
  ...vi.importActual('@/redux/agents/agentSlice'),
  useGetAgentsQuery: vi.fn(), // 👈 mock it here
}));

describe('AgentsPage', () => {
  const mockNavigate = vi.fn();
  const mockRefetch = vi.fn();
  const mockAgentsData = {data: [{id: 1, name: 'Agent <PERSON>'}]};

  beforeEach(() => {
    vi.clearAllMocks();
    (reactRouter.useNavigate as any).mockReturnValue(mockNavigate);
    (reactRedux.useGetAgentsQuery as any).mockReturnValue({
      data: mockAgentsData,
      refetch: mockRefetch,
    });
    vi.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it('renders DataTable with agents data', () => {
    render(<AgentsPage />);
    expect(screen.getByText('ADD_AGENT')).toBeInTheDocument();
    expect(screen.getByTestId('Search')).toBeInTheDocument();
  });

  it('calls navigate when Add Agent button is clicked', async () => {
    const user = userEvent.setup();
    render(<AgentsPage />);

    await user.click(screen.getByText('ADD_AGENT'));
    expect(mockNavigate).toHaveBeenCalledWith('/addAgent');
  });

  it('calls handleSearch when searching', async () => {
    const user = userEvent.setup();
    render(<AgentsPage />);

    const searchInput = screen.getByTestId('Search');
    await user.type(searchInput, 'test search');

    expect(console.log).toHaveBeenCalledWith('Search value:', 'test search');
  });

  it('displays agent data from the API', () => {
    render(<AgentsPage />);
    expect(screen.getByText('Agent Smith')).toBeInTheDocument();
  });
});
