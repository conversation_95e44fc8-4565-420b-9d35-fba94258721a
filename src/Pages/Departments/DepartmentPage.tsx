import {DataTable} from '../../components/data-table';
import {useCreateDepartmentMutation, useGetDepartmentsQuery} from '../../redux/departments/departmentSlice';
import {departmentColumns} from './DepartmentColumns';
import {useState, useMemo, useEffect} from 'react';
import type {FilterOptions, SortItem} from '../../types/filter.types';
import {FilterOperator} from '../../types/filter.types';
import type {DepartmentFormValues} from '../../validations/departmentSchema';
import {DepartmentForm} from './DepartmentForm';
import SuccessModal from '../../components/modals/SuccessModal';
import {MESSAGES} from '../../constants';
import {useTranslation} from 'react-i18next';

const DEBOUNCE_DELAY = 300;

const DepartmentPage = () => {
  const {t} = useTranslation();
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [debouncedQuery, setDebouncedQuery] = useState<string>(searchQuery);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [successModalOpen, setSuccessModalOpen] = useState<boolean>(false);

  const [createDepartment, {isLoading: isCreating}] = useCreateDepartmentMutation();

  // Debounce effect
  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedQuery(searchQuery);
    }, DEBOUNCE_DELAY);

    return () => clearTimeout(handler);
  }, [searchQuery]);

  // Create filter options based on search query
  const filterOptions = useMemo<FilterOptions>(() => {
    const order = [['createdAt', 'DESC']] as SortItem[];
    const include = [
      {
        model: 'Agent',
        as: 'agents',
      },
    ];
    if (!debouncedQuery)
      return {
        order,
        include,
      };
    return {
      where: {
        name: {
          [FilterOperator.LIKE]: `%${debouncedQuery}%`,
        },
      },
      order,
      include,
    };
  }, [debouncedQuery]);

  const {data: departments, refetch} = useGetDepartmentsQuery(filterOptions);

  const handleSearch = (value: string) => {
    setSearchQuery(value);
  };

  const handleAddDepartment = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSubmitDepartment = async (values: DepartmentFormValues) => {
    try {
      await createDepartment({name: values.name}).unwrap();
      setSuccessModalOpen(true);
      void refetch();
      handleCloseModal();
    } catch (error) {
      console.error('Failed to create department:', error);
    }
  };

  return (
    <div className="w-full">
      <DataTable
        columns={departmentColumns(t)}
        data={departments?.data ?? []}
        buttonLabel={t('ADD_DEPARTMENT')}
        searchPlaceholder={t('SEARCH')}
        onSearch={handleSearch}
        onButtonClick={handleAddDepartment}
      />
      <DepartmentForm
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={values => void handleSubmitDepartment(values)}
        isLoading={isCreating}
      />
      <SuccessModal
        isOpen={successModalOpen}
        onClose={() => {
          setSuccessModalOpen(false);
        }}
        message={t(MESSAGES.DEPARTMENT_CREATED)}
      />
    </div>
  );
};

export default DepartmentPage;
