import {useForm} from 'react-hook-form';
import {zodResolver} from '@hookform/resolvers/zod';
import {departmentSchema, type DepartmentFormValues} from '../../validations/departmentSchema';
import {Button} from '../../components/ui/button';
import {Input} from '../../components/ui/input';
import {Dialog, DialogContent, DialogTitle, DialogFooter} from '../../components/ui/dialog';
import {X} from 'lucide-react';
import {cn} from '@/lib/utils';
import {useTranslation} from 'react-i18next';
import {MESSAGES} from '@/constants';

interface DepartmentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (values: DepartmentFormValues) => void;
  isLoading?: boolean;
}

export function DepartmentForm({isOpen, onClose, onSubmit, isLoading = false}: Readonly<DepartmentFormProps>) {
  const {t} = useTranslation();
  const {
    register,
    handleSubmit,
    formState: {errors},
    reset,
  } = useForm<DepartmentFormValues>({
    resolver: zodResolver(departmentSchema),
    defaultValues: {
      name: '',
    },
  });

  const handleFormSubmit = (values: DepartmentFormValues) => {
    onSubmit(values);
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px] p-0 overflow-hidden [&>button]:hidden border-0">
        {/* Custom Header */}
        <div className="relative  px-4 py-5 rounded-t-md bg-white flex items-center justify-between border-ui-border border-b-1">
          <DialogTitle className="text-sm font-normal m-0" data-testid="department-form-title">
            {t(MESSAGES.ADD_DEPARTMENT)}
          </DialogTitle>
          <button type="button" onClick={handleClose} className=" text-default hover:text-gray-700" aria-label="Close">
            <X size={20} />
          </button>
        </div>

        {/* Form Content */}
        <form onSubmit={e => void handleSubmit(handleFormSubmit)(e)} className="px-6 py-4">
          <div className="grid gap-4">
            <Input
              id="name"
              {...register('name')}
              className={cn(
                'h-11 text-[color:var(--color-placeholders)]',
                errors.name ? 'border-red-500' : 'border-input',
              )}
              placeholder="Department Name"
              data-testid="Name"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name.message}</p>}
          </div>

          <DialogFooter className="mt-10 flex justify-end">
            <Button type="button" variant="outline" onClick={handleClose} className="uppercase font-normal">
              {t('CANCEL_ADDING')}
            </Button>
            <Button type="submit" variant="primary" disabled={isLoading} className="uppercase font-normal">
              {isLoading ? t('ADDING...') : t('ADD')}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
