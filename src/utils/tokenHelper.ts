import {setSecureItem, getSecureItem, removeItems} from './cacheHelper';

// Cache keys for token storage
const CACHE_KEYS = {
  ACCESS_TOKEN: 'accessToken',
  REFRESH_TOKEN: 'refreshToken',
  TOKEN_EXPIRATION: 'tokenExpiration',
} as const;

/**
 * Stores an access token securely in localStorage
 * @param token The token to store
 */
export const storeAccessToken = (token: string): void => {
  if (!token) return;
  setSecureItem(CACHE_KEYS.ACCESS_TOKEN, token);
};

/**
 * Retrieves and decrypts the access token from localStorage
 * @returns The decrypted access token or null if not found
 */
export const getAccessToken = (): string | null => {
  return getSecureItem(CACHE_KEYS.ACCESS_TOKEN);
};

/**
 * Stores a refresh token securely in localStorage
 * @param token The refresh token to store
 */
export const storeRefreshToken = (token: string): void => {
  if (!token) return;
  setSecureItem(CACHE_KEYS.REFRESH_TOKEN, token);
};

/**
 * Retrieves and decrypts the refresh token from localStorage
 * @returns The decrypted refresh token or null if not found
 */
export const getRefreshToken = (): string | null => {
  return getSecureItem(CACHE_KEYS.REFRESH_TOKEN);
};

/**
 * Stores token expiration time in localStorage
 * @param expiresIn Expiration time in seconds
 */
export const storeTokenExpiration = (expiresIn: number): void => {
  if (!expiresIn) return;
  const expirationTime = Date.now() + expiresIn * 1000;
  setSecureItem(CACHE_KEYS.TOKEN_EXPIRATION, expirationTime.toString());
};

/**
 * Gets token expiration time from localStorage
 * @returns Expiration time as number or null if not found
 */
export const getTokenExpiration = (): number | null => {
  const expiration = getSecureItem(CACHE_KEYS.TOKEN_EXPIRATION);
  return expiration ? parseInt(expiration, 10) : null;
};

/**
 * Checks if the current token is expired
 * @returns True if token is expired or expiration time is not found
 */
export const isTokenExpired = (): boolean => {
  const expiration = getTokenExpiration();
  if (!expiration) return true;
  return Date.now() > expiration;
};

/**
 * Clears all token-related data from localStorage
 */
export const clearTokens = (): void => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('tokenExpiration');
};

/**
 * Extracts token from URL query parameters
 * @param location The location object from react-router
 * @returns The token if found, null otherwise
 */
export const extractTokenFromUrl = (location: {search: string}): string | null => {
  const searchParams = new URLSearchParams(location.search);
  return searchParams.get('token');
};

/**
 * Removes token from URL query parameters
 * @param location The location object from react-router
 * @param navigate The navigate function from react-router
 */
export const removeTokenFromUrl = (
  location: {pathname: string; search: string},
  navigate: (to: {pathname: string; search: string}, options: {replace: boolean}) => void,
): void => {
  const searchParams = new URLSearchParams(location.search);
  searchParams.delete('token');
  navigate(
    {
      pathname: location.pathname,
      search: searchParams.toString(),
    },
    {replace: true},
  );
};
