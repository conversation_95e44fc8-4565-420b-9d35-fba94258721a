import CryptoJS from 'crypto-js';

// Secret key for encryption - in a real app, this should be stored in environment variables
const SECRET_KEY: string = (import.meta.env.VITE_TOKEN_SECRET_KEY as string) ?? 'kshdlkshdlkyerlihbrejkh4ygkq3uy4gk';

/**
 * Encrypts a token before storing it
 * @param token The token to encrypt
 * @returns The encrypted token
 */
export const encryptToken = (token: string): string => {
  return CryptoJS.AES.encrypt(token, SECRET_KEY).toString();
};

/**
 * Decrypts a stored token
 * @param encryptedToken The encrypted token
 * @returns The decrypted token or null if decryption fails
 */
export const decryptToken = (encryptedToken: string | null): string | null => {
  if (!encryptedToken) return null;

  try {
    const bytes = CryptoJS.AES.decrypt(encryptedToken, SECRET_KEY);
    return bytes.toString(CryptoJS.enc.Utf8);
  } catch (error) {
    console.error('Failed to decrypt token:', error);
    return null;
  }
};

/**
 * Stores an access token securely in localStorage
 * @param token The token to store
 */
export const storeAccessToken = (token: string): void => {
  if (!token) return;
  const encryptedToken = encryptToken(token);
  localStorage.setItem('accessToken', encryptedToken);
};

/**
 * Retrieves and decrypts the access token from localStorage
 * @returns The decrypted access token or null if not found
 */
export const getAccessToken = (): string | null => {
  const encryptedToken = localStorage.getItem('accessToken');
  return decryptToken(encryptedToken);
};

/**
 * Stores a refresh token securely in localStorage
 * @param token The refresh token to store
 */
export const storeRefreshToken = (token: string): void => {
  if (!token) return;
  const encryptedToken = encryptToken(token);
  localStorage.setItem('refreshToken', encryptedToken);
};

/**
 * Retrieves and decrypts the refresh token from localStorage
 * @returns The decrypted refresh token or null if not found
 */
export const getRefreshToken = (): string | null => {
  const encryptedToken = localStorage.getItem('refreshToken');
  return decryptToken(encryptedToken);
};

/**
 * Stores token expiration time in localStorage
 * @param expiresIn Expiration time in seconds
 */
export const storeTokenExpiration = (expiresIn: number): void => {
  if (!expiresIn) return;
  const expirationTime = Date.now() + expiresIn * 1000;
  localStorage.setItem('tokenExpiration', expirationTime.toString());
};

/**
 * Gets token expiration time from localStorage
 * @returns Expiration time as number or null if not found
 */
export const getTokenExpiration = (): number | null => {
  const expiration = localStorage.getItem('tokenExpiration');
  return expiration ? parseInt(expiration, 10) : null;
};

/**
 * Checks if the current token is expired
 * @returns True if token is expired or expiration time is not found
 */
export const isTokenExpired = (): boolean => {
  const expiration = getTokenExpiration();
  if (!expiration) return true;
  return Date.now() > expiration;
};

/**
 * Clears all token-related data from localStorage
 */
export const clearTokens = (): void => {
  localStorage.removeItem('accessToken');
  localStorage.removeItem('refreshToken');
  localStorage.removeItem('tokenExpiration');
};

/**
 * Extracts token from URL query parameters
 * @param location The location object from react-router
 * @returns The token if found, null otherwise
 */
export const extractTokenFromUrl = (location: {search: string}): string | null => {
  const searchParams = new URLSearchParams(location.search);
  return searchParams.get('token');
};

/**
 * Removes token from URL query parameters
 * @param location The location object from react-router
 * @param navigate The navigate function from react-router
 */
export const removeTokenFromUrl = (
  location: {pathname: string; search: string},
  navigate: (to: {pathname: string; search: string}, options: {replace: boolean}) => void,
): void => {
  const searchParams = new URLSearchParams(location.search);
  searchParams.delete('token');
  navigate(
    {
      pathname: location.pathname,
      search: searchParams.toString(),
    },
    {replace: true},
  );
};
