import {apiSlice} from '../apiSlice';
import type {AuthResData} from './authSlice';
import type {ApiSliceIdentifier} from '@/constants';
import type {PermissionsEnum} from '@/enums';

const apiSliceIdentifier: ApiSliceIdentifier = 'AGENT_PORTAL_FACADE_API';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface UserProfile {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  permissions: PermissionsEnum[];
  roles: string[];
}

export interface LoginResponse extends AuthResData {
  user: UserProfile;
}

export interface RefreshTokenResponse extends AuthResData {}

export const authApiSlice = apiSlice.injectEndpoints({
  endpoints: builder => ({
    login: builder.mutation<LoginResponse, LoginRequest>({
      query: credentials => ({
        url: '/auth/login',
        method: 'POST',
        body: credentials,
        apiSliceIdentifier,
      }),
    }),

    logout: builder.mutation<{message: string}, void>({
      query: () => ({
        url: '/auth/logout',
        method: 'POST',
        apiSliceIdentifier,
      }),
    }),

    refreshToken: builder.mutation<RefreshTokenResponse, RefreshTokenRequest>({
      query: ({refresh_token}) => ({
        url: '/auth/refresh',
        method: 'POST',
        body: {refresh_token},
        apiSliceIdentifier,
      }),
    }),

    getUserProfile: builder.query<UserProfile, void>({
      query: () => ({
        url: '/auth/profile',
        apiSliceIdentifier,
      }),
    }),

    validateToken: builder.query<{valid: boolean; user?: UserProfile}, void>({
      query: () => ({
        url: '/auth/validate',
        apiSliceIdentifier,
      }),
    }),

    changePassword: builder.mutation<
      {message: string},
      {currentPassword: string; newPassword: string}
    >({
      query: ({currentPassword, newPassword}) => ({
        url: '/auth/change-password',
        method: 'POST',
        body: {
          current_password: currentPassword,
          new_password: newPassword,
        },
        apiSliceIdentifier,
      }),
    }),

    requestPasswordReset: builder.mutation<{message: string}, {email: string}>({
      query: ({email}) => ({
        url: '/auth/forgot-password',
        method: 'POST',
        body: {email},
        apiSliceIdentifier,
      }),
    }),

    resetPassword: builder.mutation<
      {message: string},
      {token: string; newPassword: string}
    >({
      query: ({token, newPassword}) => ({
        url: '/auth/reset-password',
        method: 'POST',
        body: {
          token,
          new_password: newPassword,
        },
        apiSliceIdentifier,
      }),
    }),
  }),
});

export const {
  useLoginMutation,
  useLogoutMutation,
  useRefreshTokenMutation,
  useGetUserProfileQuery,
  useValidateTokenQuery,
  useChangePasswordMutation,
  useRequestPasswordResetMutation,
  useResetPasswordMutation,
} = authApiSlice;
