import type {BaseQueryFn, FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import type {RootState} from './store';
import {getBaseUrl} from './redux.helper';
import {unsetCredentials} from './auth/authSlice';
import type {ApiSliceIdentifier} from '@/constants';

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;
const baseQueryWithReauth: BaseQueryFn<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  {url: string; apiSliceIdentifier: ApiSliceIdentifier; method?: string; body?: any},
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseUrl = getBaseUrl(args.apiSliceIdentifier);

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders: (defaultHeaders, {getState}) => {
      const token = (getState() as RootState).auth.accessToken;
      if (token) {
        defaultHeaders.set('Authorization', `Bearer ${token}`);
      }

      return defaultHeaders;
    },
  });

  const result = await baseQuery(args, api, extraOptions);

  if (result.error?.status === RESULT_ERROR_STATUS) {
    api.dispatch(unsetCredentials());
  } else if (result.error) {
    // const errorMessage = getErrorMessage(result.error);
    // enqueueSnackbar(`${errorMessage}`, {variant: 'error'});
  }
  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  tagTypes: ['PlanItem'],
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
});
