import type {BaseQueryFn, FetchBaseQueryError} from '@reduxjs/toolkit/query';
import {createApi, fetchBaseQuery} from '@reduxjs/toolkit/query/react';
import type {RootState} from './store';
import {getBaseUrl} from './redux.helper';
import {unsetCredentials, setCredentials, type AuthResData} from './auth/authSlice';
import type {ApiSliceIdentifier} from '@/constants';
import {isTokenExpired} from '../utils/tokenHelper';

/**
 * Base query function with re-Authentication handling and header preparation.
 * This function serves as an interceptor for API requests with automatic token refresh.
 *
 * @param args - The fetch arguments for the request.
 * @param api - The API object provided by `createApi`.
 * @param extraOptions - Extra options for the query.
 */
const RESULT_ERROR_STATUS = 401;
const baseQueryWithReauth: BaseQueryFn<
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  {url: string; apiSliceIdentifier: ApiSliceIdentifier; method?: string; body?: any},
  unknown,
  FetchBaseQueryError
> = async (args, api, extraOptions) => {
  const baseUrl = getBaseUrl(args.apiSliceIdentifier);

  const baseQuery = fetchBaseQuery({
    baseUrl,
    prepareHeaders: (defaultHeaders, {getState}) => {
      const token = (getState() as RootState).auth.accessToken;
      if (token) {
        defaultHeaders.set('Authorization', `Bearer ${token}`);
      }
      return defaultHeaders;
    },
  });

  // Check if token is expired before making the request
  const state = api.getState() as RootState;
  if (state.auth.isLoggedIn && isTokenExpired()) {
    // Try to refresh the token
    const refreshToken = state.auth.refreshToken;
    if (refreshToken) {
      const refreshResult = await baseQuery(
        {
          url: '/auth/refresh',
          method: 'POST',
          body: {refresh_token: refreshToken},
          apiSliceIdentifier: args.apiSliceIdentifier,
        },
        api,
        extraOptions,
      );

      if (refreshResult.data) {
        // Token refresh successful, update credentials
        const authData = refreshResult.data as AuthResData;
        api.dispatch(setCredentials(authData));
      } else {
        // Token refresh failed, logout user
        api.dispatch(unsetCredentials());
        return refreshResult;
      }
    } else {
      // No refresh token available, logout user
      api.dispatch(unsetCredentials());
      return {
        error: {
          status: 401,
          data: {message: 'No refresh token available'},
        } as FetchBaseQueryError,
      };
    }
  }

  // Make the original request
  const result = await baseQuery(args, api, extraOptions);

  // Handle 401 errors (token expired during request)
  if (result.error?.status === RESULT_ERROR_STATUS) {
    const refreshToken = (api.getState() as RootState).auth.refreshToken;

    if (refreshToken) {
      // Try to refresh the token
      const refreshResult = await baseQuery(
        {
          url: '/auth/refresh',
          method: 'POST',
          body: {refresh_token: refreshToken},
          apiSliceIdentifier: args.apiSliceIdentifier,
        },
        api,
        extraOptions,
      );

      if (refreshResult.data) {
        // Token refresh successful, retry original request
        const authData = refreshResult.data as AuthResData;
        api.dispatch(setCredentials(authData));

        // Retry the original request with new token
        return await baseQuery(args, api, extraOptions);
      }
    }

    // Token refresh failed or no refresh token, logout user
    api.dispatch(unsetCredentials());
  }

  return result;
};

export const apiSlice = createApi({
  reducerPath: 'api',
  tagTypes: ['PlanItem'],
  baseQuery: baseQueryWithReauth,
  endpoints: () => ({}),
});
