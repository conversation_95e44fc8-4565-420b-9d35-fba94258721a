/**
 * Cache Keys Constants
 * 
 * This file contains all localStorage/cache key constants used throughout the application.
 * Centralizing these keys helps prevent typos and makes it easier to manage cache keys.
 */

/**
 * Authentication related cache keys
 */
export const AUTH_CACHE_KEYS = {
  /** Access token storage key */
  ACCESS_TOKEN: 'accessToken',
  /** Refresh token storage key */
  REFRESH_TOKEN: 'refreshToken',
  /** Token expiration time storage key */
  TOKEN_EXPIRATION: 'tokenExpiration',
  /** User permissions storage key */
  USER_PERMISSIONS: 'userPermissions',
  /** User profile storage key */
  USER_PROFILE: 'userProfile',
  /** Last login timestamp */
  LAST_LOGIN: 'lastLogin',
  /** Remember me preference */
  REMEMBER_ME: 'rememberMe',
} as const;

/**
 * User preferences cache keys
 */
export const USER_PREFERENCES_CACHE_KEYS = {
  /** Theme preference (light/dark) */
  THEME: 'userTheme',
  /** Language preference */
  LANGUAGE: 'userLanguage',
  /** Notification settings */
  NOTIFICATIONS: 'notificationSettings',
  /** Dashboard layout preferences */
  DASHBOARD_LAYOUT: 'dashboardLayout',
  /** Sidebar state (collapsed/expanded) */
  SIDEBAR_STATE: 'sidebarState',
  /** Table preferences (columns, sorting, etc.) */
  TABLE_PREFERENCES: 'tablePreferences',
  /** Font size preference */
  FONT_SIZE: 'fontSize',
  /** Timezone preference */
  TIMEZONE: 'timezone',
} as const;

/**
 * Application state cache keys
 */
export const APP_STATE_CACHE_KEYS = {
  /** Current route/page */
  CURRENT_ROUTE: 'currentRoute',
  /** Navigation history */
  NAVIGATION_HISTORY: 'navigationHistory',
  /** Form draft data */
  FORM_DRAFTS: 'formDrafts',
  /** Search history */
  SEARCH_HISTORY: 'searchHistory',
  /** Recently viewed items */
  RECENT_ITEMS: 'recentItems',
  /** Application version */
  APP_VERSION: 'appVersion',
  /** Feature flags */
  FEATURE_FLAGS: 'featureFlags',
  /** Debug mode state */
  DEBUG_MODE: 'debugMode',
} as const;

/**
 * Session related cache keys
 */
export const SESSION_CACHE_KEYS = {
  /** Session ID */
  SESSION_ID: 'sessionId',
  /** Session start time */
  SESSION_START: 'sessionStart',
  /** Last activity timestamp */
  LAST_ACTIVITY: 'lastActivity',
  /** Session timeout warning shown */
  TIMEOUT_WARNING_SHOWN: 'timeoutWarningShown',
  /** Idle time tracking */
  IDLE_TIME: 'idleTime',
  /** Session data */
  SESSION_DATA: 'sessionData',
} as const;

/**
 * API related cache keys
 */
export const API_CACHE_KEYS = {
  /** API base URL */
  API_BASE_URL: 'apiBaseUrl',
  /** API version */
  API_VERSION: 'apiVersion',
  /** API rate limit info */
  API_RATE_LIMIT: 'apiRateLimit',
  /** Cached API responses */
  API_RESPONSES: 'apiResponses',
  /** API request queue */
  API_REQUEST_QUEUE: 'apiRequestQueue',
  /** Offline API requests */
  OFFLINE_REQUESTS: 'offlineRequests',
} as const;

/**
 * Performance and analytics cache keys
 */
export const ANALYTICS_CACHE_KEYS = {
  /** User analytics data */
  USER_ANALYTICS: 'userAnalytics',
  /** Performance metrics */
  PERFORMANCE_METRICS: 'performanceMetrics',
  /** Error logs */
  ERROR_LOGS: 'errorLogs',
  /** Usage statistics */
  USAGE_STATS: 'usageStats',
  /** A/B testing data */
  AB_TEST_DATA: 'abTestData',
} as const;

/**
 * Temporary/cache data keys
 */
export const TEMP_CACHE_KEYS = {
  /** Temporary form data */
  TEMP_FORM_DATA: 'tempFormData',
  /** Temporary uploads */
  TEMP_UPLOADS: 'tempUploads',
  /** Temporary selections */
  TEMP_SELECTIONS: 'tempSelections',
  /** Clipboard data */
  CLIPBOARD_DATA: 'clipboardData',
  /** Temporary filters */
  TEMP_FILTERS: 'tempFilters',
} as const;

/**
 * Feature-specific cache keys
 */
export const FEATURE_CACHE_KEYS = {
  /** Chat/messaging data */
  CHAT_DATA: 'chatData',
  /** Notification queue */
  NOTIFICATION_QUEUE: 'notificationQueue',
  /** Widget configurations */
  WIDGET_CONFIG: 'widgetConfig',
  /** Custom shortcuts */
  CUSTOM_SHORTCUTS: 'customShortcuts',
  /** Bookmarks */
  BOOKMARKS: 'bookmarks',
  /** Tags */
  TAGS: 'tags',
} as const;

/**
 * All cache keys combined for easy access
 */
export const ALL_CACHE_KEYS = {
  ...AUTH_CACHE_KEYS,
  ...USER_PREFERENCES_CACHE_KEYS,
  ...APP_STATE_CACHE_KEYS,
  ...SESSION_CACHE_KEYS,
  ...API_CACHE_KEYS,
  ...ANALYTICS_CACHE_KEYS,
  ...TEMP_CACHE_KEYS,
  ...FEATURE_CACHE_KEYS,
} as const;

/**
 * Cache key categories for bulk operations
 */
export const CACHE_KEY_CATEGORIES = {
  AUTH: Object.values(AUTH_CACHE_KEYS),
  USER_PREFERENCES: Object.values(USER_PREFERENCES_CACHE_KEYS),
  APP_STATE: Object.values(APP_STATE_CACHE_KEYS),
  SESSION: Object.values(SESSION_CACHE_KEYS),
  API: Object.values(API_CACHE_KEYS),
  ANALYTICS: Object.values(ANALYTICS_CACHE_KEYS),
  TEMP: Object.values(TEMP_CACHE_KEYS),
  FEATURE: Object.values(FEATURE_CACHE_KEYS),
} as const;

/**
 * Cache keys that should be encrypted
 */
export const ENCRYPTED_CACHE_KEYS = [
  AUTH_CACHE_KEYS.ACCESS_TOKEN,
  AUTH_CACHE_KEYS.REFRESH_TOKEN,
  AUTH_CACHE_KEYS.USER_PERMISSIONS,
  AUTH_CACHE_KEYS.USER_PROFILE,
  SESSION_CACHE_KEYS.SESSION_ID,
  SESSION_CACHE_KEYS.SESSION_DATA,
  API_CACHE_KEYS.API_RATE_LIMIT,
] as const;

/**
 * Cache keys that should be cleared on logout
 */
export const LOGOUT_CLEAR_KEYS = [
  ...Object.values(AUTH_CACHE_KEYS),
  ...Object.values(SESSION_CACHE_KEYS),
  APP_STATE_CACHE_KEYS.CURRENT_ROUTE,
  APP_STATE_CACHE_KEYS.NAVIGATION_HISTORY,
  TEMP_CACHE_KEYS.TEMP_FORM_DATA,
  TEMP_CACHE_KEYS.TEMP_SELECTIONS,
] as const;

/**
 * Cache keys that should persist across sessions
 */
export const PERSISTENT_CACHE_KEYS = [
  ...Object.values(USER_PREFERENCES_CACHE_KEYS),
  AUTH_CACHE_KEYS.REMEMBER_ME,
  APP_STATE_CACHE_KEYS.SEARCH_HISTORY,
  APP_STATE_CACHE_KEYS.RECENT_ITEMS,
  FEATURE_CACHE_KEYS.BOOKMARKS,
  FEATURE_CACHE_KEYS.CUSTOM_SHORTCUTS,
] as const;

/**
 * Cache keys that should be cleared on app update
 */
export const VERSION_CLEAR_KEYS = [
  API_CACHE_KEYS.API_RESPONSES,
  APP_STATE_CACHE_KEYS.FEATURE_FLAGS,
  ANALYTICS_CACHE_KEYS.PERFORMANCE_METRICS,
  TEMP_CACHE_KEYS.TEMP_UPLOADS,
] as const;

/**
 * Type definitions for cache keys
 */
export type AuthCacheKey = typeof AUTH_CACHE_KEYS[keyof typeof AUTH_CACHE_KEYS];
export type UserPreferencesCacheKey = typeof USER_PREFERENCES_CACHE_KEYS[keyof typeof USER_PREFERENCES_CACHE_KEYS];
export type AppStateCacheKey = typeof APP_STATE_CACHE_KEYS[keyof typeof APP_STATE_CACHE_KEYS];
export type SessionCacheKey = typeof SESSION_CACHE_KEYS[keyof typeof SESSION_CACHE_KEYS];
export type ApiCacheKey = typeof API_CACHE_KEYS[keyof typeof API_CACHE_KEYS];
export type AnalyticsCacheKey = typeof ANALYTICS_CACHE_KEYS[keyof typeof ANALYTICS_CACHE_KEYS];
export type TempCacheKey = typeof TEMP_CACHE_KEYS[keyof typeof TEMP_CACHE_KEYS];
export type FeatureCacheKey = typeof FEATURE_CACHE_KEYS[keyof typeof FEATURE_CACHE_KEYS];
export type AllCacheKey = typeof ALL_CACHE_KEYS[keyof typeof ALL_CACHE_KEYS];

/**
 * Helper functions for cache key operations
 */
export const CacheKeyUtils = {
  /**
   * Check if a key should be encrypted
   */
  shouldEncrypt: (key: string): boolean => {
    return ENCRYPTED_CACHE_KEYS.includes(key as any);
  },

  /**
   * Check if a key should be cleared on logout
   */
  shouldClearOnLogout: (key: string): boolean => {
    return LOGOUT_CLEAR_KEYS.includes(key as any);
  },

  /**
   * Check if a key should persist across sessions
   */
  shouldPersist: (key: string): boolean => {
    return PERSISTENT_CACHE_KEYS.includes(key as any);
  },

  /**
   * Check if a key should be cleared on version update
   */
  shouldClearOnVersionUpdate: (key: string): boolean => {
    return VERSION_CLEAR_KEYS.includes(key as any);
  },

  /**
   * Get all keys in a category
   */
  getKeysInCategory: (category: keyof typeof CACHE_KEY_CATEGORIES): readonly string[] => {
    return CACHE_KEY_CATEGORIES[category];
  },

  /**
   * Get category for a specific key
   */
  getCategoryForKey: (key: string): string | null => {
    for (const [category, keys] of Object.entries(CACHE_KEY_CATEGORIES)) {
      if (keys.includes(key as any)) {
        return category;
      }
    }
    return null;
  },
} as const;
