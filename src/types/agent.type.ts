import type {PaginatedResponse} from './api.type';

export enum AgentAvailability {
  ONLINE = 'online',
  AWAY = 'away',
  BUSY = 'busy',
  OFFLINE = 'offline',
}

export type AgentsDetails = {
  name: string;
  email: string;
  mobile: string;
  departmentName: string;
  createdAt: string;
  availability: AgentAvailability;
  status: string;
};

export enum AgentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

export type AgentListResponse = PaginatedResponse<AgentsDetails>;
