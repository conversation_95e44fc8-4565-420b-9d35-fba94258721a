/**
 * Navigation and Location Type Definitions
 * 
 * This file contains type definitions for navigation and location objects
 * used throughout the application, particularly for URL token handling.
 */

/**
 * Simplified location interface for URL operations
 * Compatible with React Router's Location type
 */
export interface AppLocation {
  /** The pathname portion of the URL */
  pathname: string;
  /** The search/query string portion of the URL */
  search: string;
}

/**
 * Extended location interface with additional properties
 * for more comprehensive location handling
 */
export interface ExtendedAppLocation extends AppLocation {
  /** The hash portion of the URL */
  hash?: string;
  /** The state object associated with the location */
  state?: any;
  /** A unique key identifying this location */
  key?: string;
}

/**
 * Navigation target interface for programmatic navigation
 */
export interface NavigationTarget {
  /** The pathname to navigate to */
  pathname: string;
  /** The search/query string for the navigation */
  search: string;
  /** Optional hash for the navigation */
  hash?: string;
  /** Optional state to pass with the navigation */
  state?: any;
}

/**
 * Navigation options interface
 */
export interface NavigationOptions {
  /** Whether to replace the current entry in the history stack */
  replace: boolean;
  /** Optional state to pass with the navigation */
  state?: any;
}

/**
 * Navigation function type definition
 * Compatible with React Router's navigate function
 */
export type NavigateFunction = (
  to: NavigationTarget | string,
  options?: NavigationOptions
) => void;

/**
 * Simplified navigate function type for basic operations
 * Used specifically for token URL operations
 */
export type SimpleNavigateFunction = (
  to: { pathname: string; search: string },
  options: { replace: boolean }
) => void;

/**
 * URL search parameters interface
 */
export interface URLSearchParams {
  /** Get a parameter value by key */
  get(key: string): string | null;
  /** Set a parameter value */
  set(key: string, value: string): void;
  /** Delete a parameter */
  delete(key: string): void;
  /** Convert to string */
  toString(): string;
  /** Check if parameter exists */
  has(key: string): boolean;
  /** Get all values for a key */
  getAll(key: string): string[];
}

/**
 * Route parameters interface
 */
export interface RouteParams {
  [key: string]: string | undefined;
}

/**
 * Navigation state interface for storing navigation context
 */
export interface NavigationState {
  /** The location the user was trying to access before redirect */
  from?: AppLocation;
  /** Additional context data */
  context?: Record<string, any>;
  /** Timestamp of the navigation */
  timestamp?: number;
}

/**
 * Browser history interface
 */
export interface BrowserHistory {
  /** Current location */
  location: AppLocation;
  /** Navigate to a new location */
  push(to: NavigationTarget | string, state?: any): void;
  /** Replace current location */
  replace(to: NavigationTarget | string, state?: any): void;
  /** Go back in history */
  back(): void;
  /** Go forward in history */
  forward(): void;
  /** Go to specific history entry */
  go(delta: number): void;
}

/**
 * URL utility functions interface
 */
export interface URLUtils {
  /** Parse URL search parameters */
  parseSearchParams(search: string): Record<string, string>;
  /** Build search string from parameters */
  buildSearchString(params: Record<string, string>): string;
  /** Extract domain from URL */
  extractDomain(url: string): string;
  /** Check if URL is absolute */
  isAbsoluteURL(url: string): boolean;
}

/**
 * Navigation hook return type
 */
export interface UseNavigationReturn {
  /** Current location */
  location: AppLocation;
  /** Navigation function */
  navigate: NavigateFunction;
  /** Go back function */
  goBack: () => void;
  /** Go forward function */
  goForward: () => void;
  /** Check if can go back */
  canGoBack: boolean;
  /** Check if can go forward */
  canGoForward: boolean;
}

/**
 * Route configuration interface
 */
export interface RouteConfig {
  /** Route path pattern */
  path: string;
  /** Component to render */
  component: React.ComponentType<any>;
  /** Whether route requires authentication */
  requiresAuth?: boolean;
  /** Required permissions for the route */
  requiredPermissions?: string[];
  /** Route metadata */
  meta?: Record<string, any>;
}

/**
 * Navigation guard interface
 */
export interface NavigationGuard {
  /** Guard name */
  name: string;
  /** Guard function */
  guard: (to: AppLocation, from: AppLocation) => boolean | Promise<boolean>;
  /** Priority (higher numbers run first) */
  priority?: number;
}

/**
 * Breadcrumb item interface
 */
export interface BreadcrumbItem {
  /** Display label */
  label: string;
  /** Navigation path */
  path?: string;
  /** Whether item is active */
  active?: boolean;
  /** Additional metadata */
  meta?: Record<string, any>;
}

/**
 * Navigation menu item interface
 */
export interface NavigationMenuItem {
  /** Menu item ID */
  id: string;
  /** Display label */
  label: string;
  /** Navigation path */
  path?: string;
  /** Icon name or component */
  icon?: string | React.ComponentType;
  /** Child menu items */
  children?: NavigationMenuItem[];
  /** Whether item is active */
  active?: boolean;
  /** Whether item is disabled */
  disabled?: boolean;
  /** Required permissions */
  requiredPermissions?: string[];
}

/**
 * Navigation context interface
 */
export interface NavigationContext {
  /** Current route */
  currentRoute: RouteConfig | null;
  /** Navigation history */
  history: AppLocation[];
  /** Breadcrumb items */
  breadcrumbs: BreadcrumbItem[];
  /** Navigation menu items */
  menuItems: NavigationMenuItem[];
  /** Navigation guards */
  guards: NavigationGuard[];
}
