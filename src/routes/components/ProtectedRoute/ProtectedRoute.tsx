import {Navigate, useLocation} from 'react-router-dom';
import {useAuth} from '@/hooks';
import type {PermissionsEnum} from '@/enums';
import {useEffect} from 'react';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: PermissionsEnum[];
  redirectTo?: string;
  requireAuth?: boolean;
}

/**
 * ProtectedRoute component that handles authentication and authorization
 * 
 * @param children - The component(s) to render if access is granted
 * @param requiredPermissions - Array of permissions required to access the route
 * @param redirectTo - Custom redirect path (defaults to '/login')
 * @param requireAuth - Whether authentication is required (defaults to true)
 */
export const ProtectedRoute = ({
  children,
  requiredPermissions,
  redirectTo = '/login',
  requireAuth = true,
}: ProtectedRouteProps) => {
  const {isAuthenticated, hasPermission, checkTokenExpiration} = useAuth();
  const location = useLocation();

  // Check token expiration on route access
  useEffect(() => {
    if (isAuthenticated) {
      checkTokenExpiration();
    }
  }, [isAuthenticated, checkTokenExpiration, location.pathname]);

  // If authentication is not required, render children
  if (!requireAuth) {
    return <>{children}</>;
  }

  // Check if user is authenticated
  if (!isAuthenticated) {
    // Save the attempted location for redirect after login
    return (
      <Navigate
        to={redirectTo}
        state={{from: location}}
        replace
      />
    );
  }

  // Check permissions if required
  if (requiredPermissions && !hasPermission(requiredPermissions)) {
    return (
      <Navigate
        to="/not-authorized"
        state={{from: location}}
        replace
      />
    );
  }

  // All checks passed, render the protected content
  return <>{children}</>;
};

/**
 * Higher-order component version of ProtectedRoute
 */
export const withProtectedRoute = (
  Component: React.ComponentType,
  options?: Omit<ProtectedRouteProps, 'children'>
) => {
  return (props: any) => (
    <ProtectedRoute {...options}>
      <Component {...props} />
    </ProtectedRoute>
  );
};

export default ProtectedRoute;
