// src/App.tsx
import {BrowserRouter} from 'react-router-dom';
import AppRoutes from './routes/Routes';
import {getRouteConfig} from './routes/layoutRouteConfig';
import {Provider} from 'react-redux';
import {store} from './redux/store';
import {ThemeProvider} from './theme/ThemeProvider';

function App() {
  return (
    <Provider store={store}>
      <ThemeProvider>
        <BrowserRouter>
          <AppRoutes routesConfig={getRouteConfig()} />
        </BrowserRouter>
      </ThemeProvider>
    </Provider>
  );
}

export default App;
