import {useEffect, useRef, useCallback} from 'react';
import {useAuth} from '../useAuth/useAuth';
import {getTokenExpiration} from '../../utils/tokenHelper';
import {useRefreshTokenMutation} from '../../redux/auth/authApiSlice';

interface UseTokenMonitorOptions {
  /**
   * How often to check token expiration (in milliseconds)
   * Default: 60000 (1 minute)
   */
  checkInterval?: number;

  /**
   * How many minutes before expiration to attempt refresh
   * Default: 5 minutes
   */
  refreshBeforeExpiry?: number;

  /**
   * Whether to automatically refresh tokens
   * Default: true
   */
  autoRefresh?: boolean;

  /**
   * Callback when token is about to expire
   */
  onTokenExpiring?: () => void;

  /**
   * Callback when token has expired
   */
  onTokenExpired?: () => void;

  /**
   * Callback when token refresh succeeds
   */
  onRefreshSuccess?: () => void;

  /**
   * Callback when token refresh fails
   */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onRefreshError?: (error: any) => void;
}

/**
 * Hook for monitoring token expiration and handling automatic refresh
 */
export const useTokenMonitor = (options: UseTokenMonitorOptions = {}) => {
  const {
    checkInterval = 60000, // 1 minute
    refreshBeforeExpiry = 5, // 5 minutes
    autoRefresh = true,
    onTokenExpiring,
    onTokenExpired,
    onRefreshSuccess,
    onRefreshError,
  } = options;

  const {isAuthenticated, refreshToken, logout, login} = useAuth();
  const [refreshTokenMutation, {isLoading: isRefreshing}] = useRefreshTokenMutation();

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const isRefreshingRef = useRef(false);

  /**
   * Attempt to refresh the token
   */
  const attemptRefresh = useCallback(async () => {
    if (!refreshToken || isRefreshingRef.current) {
      return false;
    }

    isRefreshingRef.current = true;

    try {
      const result = await refreshTokenMutation({refresh_token: refreshToken}).unwrap();

      // Update auth state with new tokens
      login(result);

      onRefreshSuccess?.();
      return true;
    } catch (error) {
      console.error('Token refresh failed:', error);
      onRefreshError?.(error);

      // If refresh fails, logout the user
      logout();
      return false;
    } finally {
      isRefreshingRef.current = false;
    }
  }, [refreshToken, refreshTokenMutation, login, logout, onRefreshSuccess, onRefreshError]);

  /**
   * Check token expiration and handle accordingly
   */
  const checkTokenExpiration = useCallback(() => {
    if (!isAuthenticated) {
      return;
    }

    const expiration = getTokenExpiration();
    if (!expiration) {
      // No expiration time found, logout for safety
      logout();
      return;
    }

    const now = Date.now();
    const timeUntilExpiry = expiration - now;
    const refreshThreshold = refreshBeforeExpiry * 60 * 1000; // Convert to milliseconds

    if (timeUntilExpiry <= 0) {
      // Token has expired
      onTokenExpired?.();
      logout();
    } else if (timeUntilExpiry <= refreshThreshold && autoRefresh) {
      // Token is about to expire, attempt refresh
      onTokenExpiring?.();
      attemptRefresh();
    }
  }, [isAuthenticated, refreshBeforeExpiry, autoRefresh, logout, onTokenExpired, onTokenExpiring, attemptRefresh]);

  /**
   * Start monitoring token expiration
   */
  const startMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }

    intervalRef.current = setInterval(checkTokenExpiration, checkInterval);
  }, [checkTokenExpiration, checkInterval]);

  /**
   * Stop monitoring token expiration
   */
  const stopMonitoring = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  // Start/stop monitoring based on authentication status
  useEffect(() => {
    if (isAuthenticated) {
      // Check immediately when authenticated
      checkTokenExpiration();
      startMonitoring();
    } else {
      stopMonitoring();
    }

    return () => {
      stopMonitoring();
    };
  }, [isAuthenticated, startMonitoring, stopMonitoring, checkTokenExpiration]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopMonitoring();
      isRefreshingRef.current = false;
    };
  }, [stopMonitoring]);

  return {
    isRefreshing,
    checkTokenExpiration,
    attemptRefresh,
    startMonitoring,
    stopMonitoring,
  };
};
