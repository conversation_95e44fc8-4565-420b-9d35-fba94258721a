import {useSnackbar} from 'notistack';
import type {VariantType} from 'notistack';

type ToastVariant = 'default' | 'success' | 'error' | 'warning' | 'info' | 'destructive';

interface ToastOptions {
  title?: string;
  description: string;
  variant?: ToastVariant;
  duration?: number;
}

export function useToast() {
  const {enqueueSnackbar} = useSnackbar();

  const toast = ({title, description, variant = 'default', duration = 3000}: ToastOptions) => {
    // Map our variants to notistack variants
    const variantMap: Record<ToastVariant, VariantType> = {
      default: 'default',
      success: 'success',
      error: 'error',
      warning: 'warning',
      info: 'info',
      destructive: 'error',
    };

    const message = title ? `${title}: ${description}` : description;

    enqueueSnackbar(message, {
      variant: variantMap[variant],
      autoHideDuration: duration,
    });
  };

  return {toast};
}
