import {Suspense, useEffect} from 'react';
import {Route, Routes, useLocation, useNavigate} from 'react-router-dom';
import NotFound from '../../Pages/NotFound/NotFound';
import mainLayoutRouteConfig from './mainLayoutRouteConfig';
import {SidebarProvider} from '../../components/ui/sidebar';
import {AppSidebar} from '../../components/AppSidebar/AppSidebar';
import {SideBarToggleBtn, TopBar} from '../../components';
import {MESSAGES} from '@/constants/messages.constant';
import {useTranslation} from 'react-i18next';
import {useAppDispatch} from '@/hooks';
import {setCredentials, type AuthResData} from '@/redux/auth/authSlice';

const MainLayout = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const {t} = useTranslation();

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const token = searchParams.get('token');

    if (token) {
      // Save token to localStorage
      localStorage.setItem('accessToken', token);
      dispatch(setCredentials({access_token: token, refresh_token: '', expires_in: 0} as AuthResData));

      // Remove token from URL
      searchParams.delete('token');
      // eslint-disable-next-line @typescript-eslint/no-floating-promises
      navigate(
        {
          pathname: location.pathname,
          search: searchParams.toString(),
        },
        {replace: true},
      );
    }
  }, [dispatch, location, navigate]);

  return (
    <SidebarProvider
      style={
        {
          '--sidebar-width-icon': '4.5rem',
          '--sidebar-width': '14rem',
        } as React.CSSProperties
      }
    >
      <AppSidebar />
      <div className="flex-1 flex flex-col">
        <TopBar />
        <SideBarToggleBtn />
        <main className="flex-1 p-6 flex">
          <Suspense fallback={t(MESSAGES.GLOBAL_LOADING_FALLBACK)}>
            <Routes>
              {mainLayoutRouteConfig.map(({path, component: Component}) => (
                <Route key={path} path={path} element={<Component />} />
              ))}
              <Route path="*" element={<NotFound />} />
            </Routes>
          </Suspense>
        </main>
      </div>
    </SidebarProvider>
  );
};

export default MainLayout;
