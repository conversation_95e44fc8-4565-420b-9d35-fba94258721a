import {render, screen} from '@testing-library/react';
import {<PERSON><PERSON>erRouter} from 'react-router-dom';
import {beforeEach, describe, expect, test, vi} from 'vitest';
import MainLayout from '../MainLayout';

// Mock the components used in MainLayout
vi.mock('../../../components/AppSidebar/AppSidebar', () => ({
  AppSidebar: () => <div data-testid="mock-app-sidebar" />,
}));

vi.mock('../../../components', () => ({
  TopBar: () => <div data-testid="mock-top-bar" />,
}));

vi.mock('../../../components', () => ({
  TopBar: () => <div data-testid="mock-top-bar" />,
  SideBarToggleBtn: () => <div data-testid="mock-sidebar-toggle-btn" />,
}));

vi.mock('../../../Pages/NotFound/NotFound', () => ({
  default: () => <div data-testid="mock-not-found" />,
}));

vi.mock('../mainLayoutRouteConfig', () => ({
  default: [
    {path: '/home', component: () => <div data-testid="mock-home-component" />},
    {path: '/agents', component: () => <div data-testid="mock-agents-component" />},
  ],
}));

vi.mock('../../../components/ui/sidebar', () => ({
  SidebarProvider: ({children}: {children: React.ReactNode}) => (
    <div data-testid="mock-sidebar-provider">{children}</div>
  ),
  useSidebar: vi.fn(),
}));

describe('MainLayout', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test('renders SidebarProvider with correct styles', () => {
    render(
      <BrowserRouter>
        <MainLayout />
      </BrowserRouter>,
    );

    const sidebarProvider = screen.getByTestId('mock-sidebar-provider');
    expect(sidebarProvider).toBeInTheDocument();
  });

  test('renders AppSidebar component', () => {
    render(
      <BrowserRouter>
        <MainLayout />
      </BrowserRouter>,
    );

    expect(screen.getByTestId('mock-app-sidebar')).toBeInTheDocument();
  });

  test('renders TopBar and SideBarToggleBtn components', () => {
    render(
      <BrowserRouter>
        <MainLayout />
      </BrowserRouter>,
    );

    expect(screen.getByTestId('mock-top-bar')).toBeInTheDocument();
    expect(screen.getByTestId('mock-sidebar-toggle-btn')).toBeInTheDocument();
  });
});
