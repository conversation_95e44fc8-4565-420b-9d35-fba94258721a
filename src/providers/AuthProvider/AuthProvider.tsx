import {useEffect} from 'react';
import {useAuth, useTokenMonitor} from '@/hooks';
import {useToast} from '@/hooks/use-toast';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * AuthProvider component that handles global authentication logic
 * including token monitoring, automatic refresh, and user notifications
 */
export const AuthProvider = ({children}: AuthProviderProps) => {
  const {isAuthenticated, checkTokenExpiration} = useAuth();
  const {toast} = useToast();

  // Initialize token monitoring with callbacks
  const {isRefreshing} = useTokenMonitor({
    checkInterval: 60000, // Check every minute
    refreshBeforeExpiry: 5, // Refresh 5 minutes before expiry
    autoRefresh: true,
    
    onTokenExpiring: () => {
      console.log('Token is about to expire, attempting refresh...');
    },
    
    onTokenExpired: () => {
      toast({
        title: 'Session Expired',
        description: 'Your session has expired. Please log in again.',
        variant: 'warning',
      });
    },
    
    onRefreshSuccess: () => {
      console.log('Token refreshed successfully');
    },
    
    onRefreshError: (error) => {
      console.error('Token refresh failed:', error);
      toast({
        title: 'Session Error',
        description: 'Unable to refresh your session. Please log in again.',
        variant: 'error',
      });
    },
  });

  // Check token expiration on app initialization
  useEffect(() => {
    if (isAuthenticated) {
      checkTokenExpiration();
    }
  }, [isAuthenticated, checkTokenExpiration]);

  // Show loading indicator when refreshing tokens
  useEffect(() => {
    if (isRefreshing) {
      toast({
        title: 'Refreshing Session',
        description: 'Updating your authentication...',
        variant: 'info',
        duration: 2000,
      });
    }
  }, [isRefreshing, toast]);

  return <>{children}</>;
};

export default AuthProvider;
