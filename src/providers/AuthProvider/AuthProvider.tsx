import {useEffect} from 'react';
import {useAuth} from '@/hooks';

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * AuthProvider component that handles basic authentication logic
 */
export const AuthProvider = ({children}: AuthProviderProps) => {
  const {isAuthenticated, checkTokenExpiration} = useAuth();

  // Check token expiration on app initialization
  useEffect(() => {
    if (isAuthenticated) {
      checkTokenExpiration();
    }
  }, [isAuthenticated, checkTokenExpiration]);

  return <>{children}</>;
};

export default AuthProvider;
